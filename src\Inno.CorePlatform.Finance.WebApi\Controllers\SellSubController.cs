using Dapr;
using Dapr.Client;
using EasyCaching.Core;
using Inno.CorePlatform.Common.Http;
using Inno.CorePlatform.Common.Utility.Strings;
using Inno.CorePlatform.Finance.Application.ApplicationServices.Idempotency;
using Inno.CorePlatform.Finance.Application.DTOs;
using Inno.CorePlatform.Finance.Application.DTOs.InvoiceCredits;
using Inno.CorePlatform.Finance.Application.DTOs.Recognize;
using Inno.CorePlatform.Finance.Application.DTOs.SPD;
using Inno.CorePlatform.Finance.Application.LogServices.Interfaces;
using Inno.CorePlatform.Finance.Application.MgmtServices.AppService;
using Inno.CorePlatform.Finance.Application.MgmtServices.Interfaces;
using Inno.CorePlatform.Finance.Application.PortInterfaces.Clients;
using Inno.CorePlatform.Finance.Domain;
using Inno.CorePlatform.Finance.WebApi.Filters;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;

namespace Inno.CorePlatform.Finance.WebApi.Controllers
{
    /// <summary>
    /// 订阅销售Sub
    /// </summary>
    [ApiController]
    [Route("api/SellSub")]
    public class SellSubController : BaseController
    {
        public override bool EnableParameterLogging { get; set; } = true;

        //private ILogger<SellSubController> _logger;
        private ISellAppService _sellAppService = null;
        public IServiceProvider _serviceProvider;
        private DaprClient _daprClient;
        private IEasyCachingProvider _easyCaching;
        private ISPDApiClient _sPDApiClient;
        private IRecognizeReceiveAppService _recognizeReceiveService;
        private IInvoiceCreditAppService _invoiceCreditAppService;

        public SellSubController(
            //ILogger<SellSubController> logger,
            DaprClient daprClient,
            IEasyCachingProvider easyCaching,
            ISPDApiClient sPDApiClient,
            IServiceProvider serviceProvider,
            IInvoiceCreditAppService invoiceCreditAppService,
            IRecognizeReceiveAppService recognizeReceiveService, ISubLogService subLog) : base(subLog)
        {
            this._easyCaching = easyCaching;
            //this._logger = logger;
            this._serviceProvider = serviceProvider;
            this._daprClient = daprClient;
            _sPDApiClient = sPDApiClient;
            _recognizeReceiveService = recognizeReceiveService;
            _invoiceCreditAppService = invoiceCreditAppService;
        }
        /// <summary>
        /// 暂存核销或者订单修订完成事件订阅
        /// </summary> 
        /// <returns></returns>
        [HttpPost("Sale")]
        [Topic("pubsub-default", "sell-all-salefinished")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<ActionResult> Sale(SaleInput input)
        {
            var ret = new BaseResponseData<int>();
            try
            {
                var eventBus = new EventBusDTO
                {
                    BusinessCode = input.code,
                    BusinessId = input.saleId,
                    BusinessType = "销售",
                    BusinessSubType = input.saleTypeStr
                };
                if (input.saleType == 4 || input.saleType == 6)//暂存核销
                {
                    _sellAppService = _serviceProvider.GetService<ITempToSellAppService>();
                }
                else if (input.saleType == 5)//订单修订
                {
                    _sellAppService = _serviceProvider.GetService<ISellReviseAppService>();
                }
                else if (input.saleType == 7)//服务订单
                {
                    _sellAppService = _serviceProvider.GetService<ISellServiceFeeService>();
                }

                if (_sellAppService != null)
                {
                    ret = await _sellAppService.PullIn(eventBus);
                    if (ret.Code != CodeStatusEnum.Success)
                    {
                        throw new Exception(ret.Message);
                    }
                }

                return Ok(ret);
            }
            catch (Exception ex)
            {
                _logService.LogAzure("sell-all-salefinished",$"入参：{input.ToJson()},异常信息：{ex.Message}" , "暂存核销或者订单修订完成事件", Application.Enums.LogLevelEnum.Error);
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = input.ToJson(),
                    Topic = "sell-all-salefinished",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/SellSub/Sale"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 开票接口
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("ReceiveInvoiceRe")]
        [Topic("pubsub-default", "finance-finance-receiveInvoice")]
        public async Task<SPDResponse> ReceiveInvoiceRe(InoviceSpdInput input)
        {
            return await _sPDApiClient.ReceiveInvoice(input);
        }

        /// <summary>
        /// 推送SPD发票 - 重试
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SynReceiveRe")]
        [Topic("pubsub-default", "finance-finance-synReceive")]
        public async Task<SPDResponse> SynReceiveRe(RecognizeReceiveSpdInput input)
        {
            return await _sPDApiClient.SynReceive(input);
        }

        /// <summary>
        /// 认款到初始应收 - 重试
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SynReceiveNoInvoiceRe")]
        [Topic("pubsub-default", "finance-finance-synReceiveNoInvoice")]
        public async Task<SPDResponse> SynReceiveNoInvoiceRe(RecognizeReceiveSpdInitInput input)
        {
            return await _sPDApiClient.synReceiveNoInvoice(input);
        }

        /// <summary>
        ///认款到初始应收-- 重试
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("PushRecognizeReceive")]
        [Topic("pubsub-default", "finance-finance-pushRecognizeReceive")]
        public async Task<SPDResponse> PushRecognizeReceive(RecognizeReceiveSpdInitInput input)
        {
            return await _sPDApiClient.synReceiveNoInvoice(input);
        }

        /// <summary>
        /// 认款数据 - 重试
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SynReceiveAndInvoiceRe")]
        [Topic("pubsub-default", "finance-finance-synReceiveAndInvoice")]
        public async Task<SPDResponse> SynReceiveAndInvoiceRe(RecognizeReceivePushBusinessInput input)
        {
            var res = await _sPDApiClient.synReceiveAndInvoice(input);
            if (res.code == 0)
            {
                //成功更改状态
                var dto = new RecognizeReceiveApproveInput()
                {
                    Code = input.confirmCode
                };
                var result = await _recognizeReceiveService.Approve(dto);
                if (result < 0)
                {
                    return new SPDResponse()
                    {
                        code = 0,
                        msg = "推送商务平台成功，状态更改失败"
                    };
                }
            }
            return res;
        }


        /// <summary>
        /// 设置阳采发票
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("SetSunPurchase")]
        [Topic("pubsub-default", "sale-finance-setsunpurchase")]
        public async Task<ActionResult> SetSunPurchase(SetSunPurchaseInput input)
        {
            var daigId = Guid.NewGuid();
            var jsonStr = JsonConvert.SerializeObject(input);
            var cachekey = "sell-all-salefinished_" + input.OrderNos.FirstOrDefault();
            var ret = new BaseResponseData<int>();
            try
            {
                var hasOpt = await _easyCaching.GetAsync<string>(cachekey);
                if (hasOpt == null || string.IsNullOrEmpty(hasOpt.Value))
                {
                    //todo
                    ret = await _invoiceCreditAppService.SetSunPurchase(input);
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _easyCaching.Remove(cachekey);
                _logService.LogAzure("sale-finance-setsunpurchase", $"入参：{input.ToJson()},异常信息：{ex.Message}", "设置阳采发票", Application.Enums.LogLevelEnum.Error);
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "sale-finance-setsunpurchase",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/SellSub/SetSunPurchase"  //重试的回调方法路由 
                });
                return Ok();
            }
        }

        /// <summary>
        /// 根据订单号撤销认款
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("CancelReceiveByOrderNo")]
        [Topic("pubsub-default", "sale-fin-cancelReceiveByOrderNo")]
        public async Task<BaseResponseData<int>> CancelReceiveByOrderNo(CancelReceiveByOrderNoInput input)
        {
            if (input == null)
            {
                return BaseResponseData<int>.Failed(500, "操作失败，原因：入参为空");
            }
            var jsonStr = JsonConvert.SerializeObject(input);
            try
            {
                return await _recognizeReceiveService.CancelReceiveByOrderNo(input.orderNo);
            }
            catch (Exception ex)
            {
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = jsonStr,
                    Topic = "sale-fin-cancelReceiveByOrderNo",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/SellSub/CancelReceiveByOrderNo?orderNo"  //重试的回调方法路由
                });
                return BaseResponseData<int>.Failed(500, "操作失败，原因：" + ex.Message);
            }
        }

        /// <summary>
        /// 处理销售单号变更事件
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        [HttpPost("BillCodeChange")]
        [Topic("pubsub-default", "sell-all-billcodechange")]
        [IdempotentWithBackoff(KeyGeneratorType = typeof(CustomIdempotencyKeyGenerator))]
        public async Task<ActionResult> BillCodeChange(SaleBillCodeChangeInput input)
        {
            var ret = new BaseResponseData<int>();
            try
            {
                // 处理单号变更逻辑
                ret = await _recognizeReceiveService.UpdateBillCodeInRecognizeReceiveDetail(input);

                if (ret.Code != CodeStatusEnum.Success)
                {
                    throw new Exception(ret.Message);
                }
                return Ok(ret);
            }
            catch (Exception ex)
            {
                _logService.LogAzure("sell-all-billcodechange", $"入参：{input.ToJson()},异常信息：{ex.Message}", "处理销售单号变更事件", Application.Enums.LogLevelEnum.Error);
                await _daprClient.InvokeBindingAsync("binding-output-failuremsg", "create", new FailureMsgInput
                {
                    AppId = "finance-webapi",
                    MsgBody = input.ToJson(),
                    Topic = "sell-all-billcodechange",
                    FailReason = ex.Message,
                    ExceptionMessage = JsonConvert.SerializeObject(ex),
                    CallBackMethodRoute = "/api/SellSub/BillCodeChange"  //重试的回调方法路由
                });
                return Ok();
            }
        }
    }

    /// <summary>
    /// 销售订单事件入参
    /// </summary>
    public class SaleInput
    {
        /// <summary>
        /// 销售单号
        /// </summary>
        public string? code { get; set; }
        /// <summary>
        /// 销售Id
        /// </summary>
        public Guid? saleId { get; set; }
        /// <summary>
        /// 销售类型 saleType: 1 销售出库订单 4 暂存核销订单 5 订单修订,6 跟台核销订单 7 服务订单
        /// </summary>
        public int? saleType { get; set; }

        public string saleTypeStr
        {
            get
            {
                var ret = string.Empty;
                switch (saleType)
                {
                    case 1:
                        ret = "销售出库订单";
                        break;
                    case 2:
                        ret = "B类订单";
                        break;
                    case 3:
                        ret = "销售出库订单";
                        break;
                    case 4:
                        ret = "暂存核销订单";
                        break;
                    case 5:
                        ret = "订单修订";
                        break;
                    case 6:
                        ret = "跟台核销";
                        break;
                    case 7:
                        ret = "服务订单";
                        break;
                    default:
                        break;
                }
                return ret;

            }
        }
    }


}
